<template>
	<view class="container">
		<!-- 状态栏占位 -->
		<view class="status-bar" :style="{height: statusBarHeight + 'px'}"></view>

		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<text class="navbar-title">智能芯桌牌</text>
				<view class="navbar-right">
					<view class="weather-info">
						<text class="weather-text">{{ weatherText }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 欢迎卡片 -->
			<view class="welcome-card">
				<view class="welcome-content">
					<view class="welcome-text">
						<text class="greeting">{{ greeting }}</text>
						<text class="welcome-desc">欢迎使用智能芯桌牌系统</text>
					</view>
					<view class="welcome-icon">
						<text class="icon-bluetooth">📶</text>
					</view>
				</view>
				<view class="card-decoration"></view>
			</view>

			<!-- 快速功能区 -->
			<view class="quick-actions">
				<text class="section-title">快速功能</text>
				<view class="action-grid">
					<view class="action-item" @click="goToSingleScreen">
						<view class="action-icon single">
							<text class="icon">📱</text>
						</view>
						<text class="action-text">单一投屏</text>
					</view>

					<view class="action-item" @click="goToBatchScreen">
						<view class="action-icon batch">
							<text class="icon">📺</text>
						</view>
						<text class="action-text">批量投屏</text>
					</view>

					<view class="action-item" @click="goToTemplate">
						<view class="action-icon template">
							<text class="icon">🎨</text>
						</view>
						<text class="action-text">模板管理</text>
					</view>

					<view class="action-item" @click="goToMeeting">
						<view class="action-icon meeting">
							<text class="icon">📅</text>
						</view>
						<text class="action-text">会议管理</text>
					</view>
				</view>
			</view>

			<!-- 统计信息卡片 -->
			<view class="stats-card">
				<text class="card-title">今日统计</text>
				<view class="stats-content">
					<view class="stat-item">
						<text class="stat-number">{{ todayScreens }}</text>
						<text class="stat-label">投屏次数</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-number">{{ connectedDevices }}</text>
						<text class="stat-label">连接设备</text>
					</view>
					<view class="stat-divider"></view>
					<view class="stat-item">
						<text class="stat-number">{{ activeMeetings }}</text>
						<text class="stat-label">活跃会议</text>
					</view>
				</view>
			</view>

			<!-- 最近使用 -->
			<view class="recent-section">
				<view class="section-header">
					<text class="section-title">最近使用</text>
					<text class="more-text" @click="viewMore">查看更多</text>
				</view>
				<view class="recent-list">
					<view class="recent-item" v-for="(item, index) in recentItems" :key="index" @click="openRecentItem(item)">
						<view class="recent-icon">
							<text class="icon">{{ item.icon }}</text>
						</view>
						<view class="recent-info">
							<text class="recent-title">{{ item.title }}</text>
							<text class="recent-time">{{ item.time }}</text>
						</view>
						<text class="arrow">›</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 背景装饰 -->
		<view class="background-decoration">
			<view class="bg-circle circle1"></view>
			<view class="bg-circle circle2"></view>
			<view class="bg-circle circle3"></view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: 0,
				greeting: '早上好',
				weatherText: '晴 22°C',
				todayScreens: 12,
				connectedDevices: 8,
				activeMeetings: 3,
				recentItems: [
					{
						icon: '📱',
						title: '会议室A - 投屏',
						time: '2分钟前'
					},
					{
						icon: '🎨',
						title: '新建模板',
						time: '10分钟前'
					},
					{
						icon: '📅',
						title: '周例会',
						time: '1小时前'
					}
				]
			}
		},
		onLoad() {
			// 页面加载时的处理逻辑
			this.initPage();
		},
		methods: {
			// 初始化页面
			initPage() {
				this.getSystemInfo();
				this.checkLoginStatus();
				this.updateGreeting();
				this.loadStatistics();
			},

			// 获取系统信息
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				this.statusBarHeight = systemInfo.statusBarHeight || 0;
			},

			// 更新问候语
			updateGreeting() {
				const hour = new Date().getHours();
				if (hour < 12) {
					this.greeting = '早上好';
				} else if (hour < 18) {
					this.greeting = '下午好';
				} else {
					this.greeting = '晚上好';
				}
			},

			// 加载统计数据
			loadStatistics() {
				// 这里可以调用API获取真实数据
				// 暂时使用模拟数据
			},

			// 检查登录状态
			checkLoginStatus() {
				const token = uni.getStorageSync('token');
				if (!token) {
					uni.navigateTo({
						url: '/pages/login/account-login'
					});
				}
			},

			// 导航到单一投屏页面
			goToSingleScreen() {
				uni.navigateTo({
					url: '/pages/subPackage/public/single-screen'
				});
			},

			// 导航到批量投屏页面
			goToBatchScreen() {
				uni.navigateTo({
					url: '/pages/subPackage/public/batch-screen'
				});
			},

			// 导航到模板管理
			goToTemplate() {
				uni.navigateTo({
					url: '/pages/subPackage/template/edit_new'
				});
			},

			// 导航到会议管理
			goToMeeting() {
				uni.navigateTo({
					url: '/pages/subPackage/meeting/list'
				});
			},

			// 查看更多
			viewMore() {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},

			// 打开最近使用项目
			openRecentItem(item) {
				uni.showToast({
					title: `打开 ${item.title}`,
					icon: 'none'
				});
			}
		}
	}
</script>


<style lang="scss">
.container {
	position: relative;
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	overflow-x: hidden;
}

.status-bar {
	width: 100%;
	background: transparent;
}

.custom-navbar {
	position: relative;
	z-index: 100;
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10px);
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 40rpx;
	height: 88rpx;
}

.navbar-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #FFFFFF;
}

.navbar-right {
	display: flex;
	align-items: center;
}

.weather-info {
	background: rgba(255, 255, 255, 0.2);
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
}

.weather-text {
	font-size: 24rpx;
	color: #FFFFFF;
}

.content {
	padding: 40rpx 30rpx;
	padding-bottom: 120rpx;
}

/* 欢迎卡片 */
.welcome-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 24rpx;
	margin-bottom: 40rpx;
	position: relative;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.welcome-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx;
}

.welcome-text {
	flex: 1;
}

.greeting {
	font-size: 48rpx;
	font-weight: bold;
	color: #2C3E50;
	display: block;
	margin-bottom: 8rpx;
}

.welcome-desc {
	font-size: 28rpx;
	color: #666;
	display: block;
}

.welcome-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-bluetooth {
	font-size: 40rpx;
}

.card-decoration {
	position: absolute;
	right: -20rpx;
	top: -20rpx;
	width: 100rpx;
	height: 100rpx;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
	border-radius: 50rpx;
}

/* 快速功能区 */
.quick-actions {
	margin-bottom: 40rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #FFFFFF;
	margin-bottom: 24rpx;
	display: block;
}

.action-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 20rpx;
}

.action-item {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-item:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.action-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
}

.action-icon.single {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-icon.batch {
	background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.action-icon.template {
	background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.action-icon.meeting {
	background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.action-icon .icon {
	font-size: 36rpx;
}

.action-text {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

/* 统计信息卡片 */
.stats-card {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 40rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 24rpx;
	display: block;
}

.stats-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	color: #667eea;
	margin-bottom: 8rpx;
	display: block;
}

.stat-label {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.stat-divider {
	width: 2rpx;
	height: 60rpx;
	background: #eee;
	margin: 0 20rpx;
}

/* 最近使用 */
.recent-section {
	margin-bottom: 40rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.more-text {
	font-size: 26rpx;
	color: rgba(255, 255, 255, 0.8);
}

.recent-list {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.recent-item {
	display: flex;
	align-items: center;
	padding: 24rpx 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;
}

.recent-item:last-child {
	border-bottom: none;
}

.recent-item:active {
	background-color: #f8f9fa;
}

.recent-icon {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
}

.recent-icon .icon {
	font-size: 28rpx;
}

.recent-info {
	flex: 1;
}

.recent-title {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 4rpx;
	display: block;
}

.recent-time {
	font-size: 24rpx;
	color: #999;
	display: block;
}

.arrow {
	font-size: 32rpx;
	color: #ccc;
}

/* 背景装饰 */
.background-decoration {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;
	z-index: -1;
}

.bg-circle {
	position: absolute;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.05);
}

.circle1 {
	width: 300rpx;
	height: 300rpx;
	top: 20%;
	right: -100rpx;
	animation: float1 20s infinite ease-in-out;
}

.circle2 {
	width: 200rpx;
	height: 200rpx;
	bottom: 30%;
	left: -50rpx;
	animation: float2 15s infinite ease-in-out;
}

.circle3 {
	width: 150rpx;
	height: 150rpx;
	top: 60%;
	right: 20%;
	animation: float3 25s infinite ease-in-out;
}

@keyframes float1 {
	0%, 100% { transform: translateY(0px) rotate(0deg); }
	50% { transform: translateY(-30rpx) rotate(180deg); }
}

@keyframes float2 {
	0%, 100% { transform: translateX(0px) rotate(0deg); }
	50% { transform: translateX(20rpx) rotate(-180deg); }
}

@keyframes float3 {
	0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
	33% { transform: translate(20rpx, -20rpx) rotate(120deg); }
	66% { transform: translate(-20rpx, 10rpx) rotate(240deg); }
}
</style>
